package wrapper

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	sayelog "github.com/sayeworldevelopment/wp-core/pkg/log"
)

type PresenceWebhookPayload struct {
	SessionID   string `json:"session_id"`
	PhoneNumber string `json:"phone_number"`
}

// SendPresenceCall sends presence information to the webhook endpoint asynchronously
func SendPresenceCall(sessionID, phoneNumber string) {
	go func() {
		onwaUrl := config.ReadedConfig.App.OnwaUrl
		if onwaUrl == "" {
			sayelog.CreateLog(&entities.Log{
				Title:   "Webhook URL not configured",
				Message: "onwa_url is not set in config",
				Entity:  "webhook",
				Type:    "warning",
				Ip:      "system",
			})
			return
		}

		payload := PresenceWebhookPayload{
			SessionID:   sessionID,
			PhoneNumber: phoneNumber,
		}

		jsonData, err := json.Marshal(payload)
		if err != nil {
			sayelog.CreateLog(&entities.Log{
				Title:   "Failed to marshal webhook payload",
				Message: "Error: " + err.Error(),
				Entity:  "webhook",
				Type:    "error",
				Ip:      "system",
			})
			return
		}

		webhookURL := onwaUrl + "/api/v1/notification/webhook"
		req, err := http.NewRequest("POST", webhookURL, bytes.NewBuffer(jsonData))
		if err != nil {
			sayelog.CreateLog(&entities.Log{
				Title:   "Failed to create webhook request",
				Message: "Error: " + err.Error(),
				Entity:  "webhook",
				Type:    "error",
				Ip:      "system",
			})
			return
		}

		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("onwa_id", config.ReadedConfig.App.OnwaId)

		client := &http.Client{
			Timeout: 10 * time.Second,
		}

		resp, err := client.Do(req)
		if err != nil {
			sayelog.CreateLog(&entities.Log{
				Title:   "Failed to send webhook request",
				Message: "Error: " + err.Error(),
				Entity:  "webhook",
				Type:    "error",
				Ip:      "system",
			})
			return
		}
		defer resp.Body.Close()

		if resp.StatusCode >= 200 && resp.StatusCode < 300 {
			sayelog.CreateLog(&entities.Log{
				Title:   "Webhook sent successfully",
				Message: fmt.Sprintf("Status: %d, SessionID: %s, Phone: %s", resp.StatusCode, sessionID, phoneNumber),
				Entity:  "webhook",
				Type:    "info",
				Ip:      "system",
			})
		} else {
			sayelog.CreateLog(&entities.Log{
				Title:   "Webhook request failed",
				Message: fmt.Sprintf("HTTP Status: %d, SessionID: %s, Phone: %s", resp.StatusCode, sessionID, phoneNumber),
				Entity:  "webhook",
				Type:    "error",
				Ip:      "system",
			})
		}
	}()
}
