package middleware

import (
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/localizer"
	"github.com/sayeworldevelopment/wp-core/pkg/state"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"
)

// Rate limiting için basit in-memory store
type rateLimiter struct {
	requests map[string][]time.Time
	mutex    sync.RWMutex
}

var limiter = &rateLimiter{
	requests: make(map[string][]time.Time),
}

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("CurrentIP", c.ClientIP())
		c.Set(state.CurrentUserIP, c.ClientIP())
		c.Next()
	}
}

func XApiKey() gin.HandlerFunc {
	return func(c *gin.Context) {
		client_id := c.Get<PERSON>eader("X-Api-Key")
		if client_id == config.InitConfig().App.XApiKey {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_unauthorized_client", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

func AdminKey() gin.HandlerFunc {
	return func(c *gin.Context) {
		client_id := c.GetHeader("Admin-Key")
		if client_id == config.InitConfig().App.AdminKey {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_unauthorized_client", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

// create middleware for x-api-key

func Authorized() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg_app := config.InitConfig().App
		jwt := utils.JwtWrapper{
			Issuer:    cfg_app.JwtIssuer,
			SecretKey: cfg_app.JwtSecret,
		}
		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		if !strings.HasPrefix(bearer, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_incorrect_format_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		token := strings.Split(bearer, "Bearer ")[1]
		if jwt.ValidateToken(token) {
			claims, _ := jwt.ParseToken(token)
			c.Set(state.CurrentUserID, claims.UserID)
			c.Set(state.CurrentDeviceID, claims.DeviceID)
			c.Set(state.CurrentUserIP, c.ClientIP())
			c.Set(state.CurrentTimezone, claims.Timezone)
			c.Set(state.CurrentPhoneLanguage, claims.PhoneLanguage)
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_invalid_or_expired_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

// RateLimit middleware - IP başına dakikada maksimum istek sayısını sınırlar
func RateLimit(maxRequests int, windowDuration time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		now := time.Now()

		limiter.mutex.Lock()
		defer limiter.mutex.Unlock()

		// Bu IP için geçmiş istekleri al
		requests, exists := limiter.requests[clientIP]
		if !exists {
			requests = []time.Time{}
		}

		// Eski istekleri temizle (window dışında kalanlar)
		var validRequests []time.Time
		for _, reqTime := range requests {
			if now.Sub(reqTime) < windowDuration {
				validRequests = append(validRequests, reqTime)
			}
		}

		// Eğer limit aşılmışsa, isteği reddet
		if len(validRequests) >= maxRequests {
			c.AbortWithStatusJSON(http.StatusTooManyRequests, gin.H{
				"error":   "Rate limit exceeded",
				"message": "Too many requests, please try again later",
			})
			return
		}

		// Yeni isteği ekle
		validRequests = append(validRequests, now)
		limiter.requests[clientIP] = validRequests

		c.Next()
	}
}

// CustomRecovery - Panic durumlarında detaylı log alan recovery middleware
func CustomRecovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		// Panic durumunda detaylı log
		log.Printf("PANIC RECOVERED: %v\nRequest: %s %s\nIP: %s\nHeaders: %v",
			recovered,
			c.Request.Method,
			c.Request.URL.Path,
			c.ClientIP(),
			c.Request.Header)

		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"error": "Internal server error",
		})
	})
}
