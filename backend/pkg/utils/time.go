package utils

import (
	"strings"
	"time"
)

func GetNow() string {
	return time.Now().Format("2006-01-02 15:04:05 -0700 -07")
}

func StringToTime(rawDate string) time.Time {
	rawDate = strings.ReplaceAll(rawDate, "T", " ")
	rawDate = strings.ReplaceAll(rawDate, "Z", " ")
	rawDate = rawDate + " +0300 +03"
	rawDateTime, _ := time.Parse("2006-01-02 15:04:05 -0700 -07", rawDate)

	return rawDateTime
}

// ConvertTimeToTimezone converts a time to the specified timezone
func ConvertTimeToTimezone(t time.Time, timezone string) time.Time {
	if t.IsZero() {
		return t
	}

	// Load the target timezone
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		// If timezone is invalid, return the original time
		return t
	}

	// Convert the time to the target timezone
	return t.In(loc)
}

// ConvertTimeToTimezonePtr converts a time pointer to the specified timezone
func ConvertTimeToTimezonePtr(t *time.Time, timezone string) *time.Time {
	if t == nil || t.<PERSON>() {
		return t
	}

	converted := ConvertTimeToTimezone(*t, timezone)
	return &converted
}
