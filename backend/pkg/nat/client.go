package nat

import (
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"log"
	"strconv"
	"strings"

	waProto "go.mau.fi/whatsmeow/binary/proto"

	"github.com/nats-io/nats.go"
	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/consts"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/types"
	"google.golang.org/protobuf/proto"
)

var (
	nc         *nats.Conn
	err        error
	WaConnects = make(map[string]*whatsmeow.Client)
	waDebug    = flag.String("wadebug", "", "Enable whatsmeow debug (INFO or DEBUG)")
)

func Connect(nat config.Nats) {
	nc, err = nats.Connect(fmt.Sprintf("nats://%v:%v", nat.Host, nat.Port), nats.Name("vatansoft-wp-api"))
	if err != nil {
		log.Fatalf("Can't connect to nats: %s", err)
	}
	SendSingleMessage()
	FailedSaveDatabase()
}

func Client() *nats.Conn {
	return nc
}

func SaveMessage() {
	nc.Subscribe("save-message", func(msg *nats.Msg) {
		var (
			message = entities.Message{}
			req     dtos.AddNatsMessage
			db      = database.DBClient()
		)
		err := json.Unmarshal(msg.Data, &req)
		if err != nil {
			return
		}
		message.Mapper(req)
		message.IsSended = true
		err = db.Save(&message).Error
		req.MsgId = message.ID
		if err != nil {

			return
		}

		data, err := json.Marshal(req)
		if err != nil {

			log.Println("cannot marshal data, err: " + err.Error())
			return
		}
		err = nc.Publish("single-message", data)
		if err != nil {
			log.Println("send callback error: " + err.Error())
		}

	})
	if err == nil {
		log.Println("save message db nat subscribed")
	}
}

func SendSingleMessage() {
	nc.Subscribe("single-message", func(msg *nats.Msg) {
		var (
			req    dtos.AddNatsMessage
			ctx    = context.Background()
			client *whatsmeow.Client
			isLog  bool
		)
		err := json.Unmarshal(msg.Data, &req)
		if err != nil {
			return
		}

		var num string
		if !strings.HasPrefix(req.To, "+") {
			num = "+" + req.To
		}
		isValid, err := utils.ValidatePhoneNumber(num)
		if err != nil || !isValid {
			return
		}

		client = WaConnects[req.RegId]
		if client == nil {
			w := wrapper.WpClient()
			client, isLog, _ = w.CheckDevice(ctx, req.JID, req.RegId)
			if !isLog {
				return
			}
			WaConnects[req.RegId] = client
		}
		//TODO: send message added
	})
	if err == nil {
		log.Println("single message nat subscribed")
	}
}

func FailedSaveDatabase() {
	nc.Subscribe("save-failed", func(msg *nats.Msg) {
		var (
			message = entities.Message{}
			db      = database.DBClient()
		)
		err := json.Unmarshal(msg.Data, &message)
		if err != nil {
			return
		}
		err = db.Save(&message).Error
		if err != nil {
			return
		}
	})
	if err == nil {
		log.Println("failed save db nat subscribed")
	}
}

func SendMessage(ctx context.Context, req dtos.AddNatsMessage) error {
	db := database.DBClient()
	client := WaConnects[req.RegId]
	if client == nil {
		log.Println("error send message: client isn't logged in")
		err = db.Model(&entities.Message{}).Where("id = ?", req.MsgId).Update("status", consts.Status[consts.Failed]).Error
		//TODO: send callback for failed message save logs
		if err != nil {
			return err
		}
		return errors.New("device isn't logged in")
	}
	client.EnableAutoReconnect = true
	client.Connect()

	message := &waProto.Message{
		Conversation: proto.String(req.Message),
	}

	to := types.NewJID(req.To, types.DefaultUserServer)
	_, err = client.SendMessage(ctx, to, message)
	if err != nil {
		return err
	}
	err = db.Model(&entities.Message{}).Where("id = ?", req.MsgId).Update("status", consts.Status[consts.Delivered]).Error
	if err != nil {
		return err
	}
	log.Println("message sent successfully", req.To, req.Message)

	return nil
}

func ParseNumberFromJID(jid string) (string, error) {
	parts := strings.Split(jid, "@")
	if len(parts) < 2 {
		return "", fmt.Errorf("geçersiz JID formatı: %s", jid)
	}
	return parts[0], nil
}

func AddNewSubscription(ctx context.Context, jId string, phoneToSubscribe string) error {
	w := wrapper.WpClient()
	pjid, _ := types.ParseJID(jId)
	device, err := w.MContainer.GetDevice(ctx, pjid)
	if err != nil {
		return err
	}
	if device == nil {
		return errors.New("device not found")
	}

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", jId).
		First(&session).Error; err != nil {
		return err
	}

	// Mevcut client'ı kullan
	regId := strconv.FormatUint(uint64(device.RegistrationID), 10)
	client := WaConnects[regId]

	if client == nil {
		// Client yoksa önce SubscribeDevicePresences çağrılmalı
		return errors.New("client not initialized, call SubscribeDevicePresences first")
	}

	// Yeni subscribe ekle
	subJid := phoneToSubscribe + "@s.whatsapp.net"
	parsedJID, _ := types.ParseJID(subJid)
	err = client.SubscribePresence(parsedJID)
	if err != nil {
		return err
	}

	// Veritabanına kaydet
	subscribe := entities.SessionSubscription{
		SessionID:      session.ID,
		Phone:          phoneToSubscribe,
		JID:            subJid,
		Active:         true,
		EventHandlerId: 0, // EventHandlerId client'a bağlı, tek bir handler var
	}

	return db.Create(&subscribe).Error
}
