package server

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"path"
	"syscall"
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/sayeworldevelopment/wp-core/app/api/routes"
	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/domains/proxy"
	"github.com/sayeworldevelopment/wp-core/pkg/domains/session"
	"github.com/sayeworldevelopment/wp-core/pkg/domains/wp"

	"github.com/sayeworldevelopment/wp-core/pkg/embed"
	"github.com/sayeworldevelopment/wp-core/pkg/middleware"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"github.com/swaggo/swag/example/basic/docs"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

var (
	swaggerUser string
	swaggerPass string
)

func LaunchHttpServer(appc config.App, allows config.Allows) {
	log.Println("Starting HTTP Server...")
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(log gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			log.TimeStamp.Format("2006-01-02 15:04:05"),
			log.ClientIP,
			log.Method,
			log.Path,
			log.Request.Proto,
			log.StatusCode,
			log.Latency,
		)
	}))
	//app.Use(middleware.CustomRecovery())
	app.Use(otelgin.Middleware(appc.Name))
	app.Use(middleware.ClaimIp())

	// Rate limiting: IP başına dakikada 100 istek
	//app.Use(middleware.RateLimit(100, time.Minute))

	//app.Use(middleware.Secure())
	app.Use(cors.New(cors.Config{
		AllowMethods:     allows.Methods,
		AllowHeaders:     allows.Headers,
		AllowOrigins:     allows.Origins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(
		ginprom.Engine(app),
		ginprom.Subsystem("gin"),
		ginprom.Path("/metrics"),
		ginprom.Ignore("/swagger/*any"),
	)
	app.Use(p.Instrument())

	db := database.DBClient()

	// -----> Routes Start
	api := app.Group("/api/v1")

	wpClient := wrapper.WpClient()
	wp_repo := wp.NewRepo(db)
	wp_service := wp.NewService(wp_repo, wpClient)
	routes.WPRoutes(api, wp_service)

	session_repo := session.NewRepository(db)
	session_service := session.NewService(session_repo, wpClient)
	routes.SessionRoutes(api, session_service, wp_service)

	proxy_repo := proxy.NewRepo(db)
	proxy_service := proxy.NewService(proxy_repo)
	routes.ProxyRoutes(api, proxy_service)

	// Routes End <-----

	app.GET("/docs", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "docs/index.html")
	})

	if os.Getenv("SWAGGER_USER") != "" {
		swaggerUser = os.Getenv("SWAGGER_USER")
	} else {
		swaggerUser = "saye-dev"
	}
	if os.Getenv("SWAGGER_PASS") != "" {
		swaggerPass = os.Getenv("SWAGGER_PASS")
	} else {
		swaggerPass = "saye-dev"
	}

	docs.SwaggerInfo.Host = config.InitConfig().App.BaseUrl
	docs.SwaggerInfo.Version = os.Getenv("APP_VERSION")
	app.GET("/docs/*any",
		gin.BasicAuth(gin.Accounts{
			swaggerUser: swaggerPass,
		}),
		ginSwagger.WrapHandler(swaggerFiles.Handler),
	)

	app.GET("/assets/*filepath", func(c *gin.Context) {
		c.FileFromFS(path.Join("dist", c.Request.URL.Path), http.FS(embed.StaticsFS()))
	})
	app.Any("/", func(c *gin.Context) {
		c.FileFromFS("dist/", http.FS(embed.StaticsFS()))
	})
	app.GET("/robots.txt", func(c *gin.Context) {
		c.FileFromFS("dist/robots.txt", http.FS(embed.StaticsFS()))
	})
	app.GET("/favicon.ico", func(c *gin.Context) {
		c.FileFromFS("dist/favicon.ico", http.FS(embed.StaticsFS()))
	})
	app.NoRoute(func(c *gin.Context) {
		c.FileFromFS("dist/", http.FS(embed.StaticsFS()))
	})

	fmt.Println("Server is running on port " + appc.Port)

	// HTTP Server timeout ayarları ile
	server := &http.Server{
		Addr:           net.JoinHostPort(appc.Host, appc.Port),
		Handler:        app,
		ReadTimeout:    30 * time.Second,  // Request okuma timeout'u
		WriteTimeout:   30 * time.Second,  // Response yazma timeout'u
		IdleTimeout:    120 * time.Second, // Keep-alive timeout'u
		MaxHeaderBytes: 1 << 20,           // 1MB max header size
	}

	// Graceful shutdown için goroutine
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()

	// Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited")
}
