package config

import (
	"os"

	"gopkg.in/yaml.v3"
)

type Config struct {
	App        App        `yaml:"app"`
	Redis      Redis      `yaml:"redis"`
	Database   Database   `yaml:"database"`
	Cloudinary Cloudinary `yaml:"cloudinary"`
	Allows     Allows     `yaml:"allows"`
	Whatsapp   Whatsapp   `yaml:"whatsapp"`
	Nats       Nats       `yaml:"nats"`
	BrightData BrightData `yaml:"brightdata"`
}

type App struct {
	Name            string `yaml:"name"`
	Port            string `yaml:"port"`
	Host            string `yaml:"host"`
	BaseUrl         string `yaml:"base_url"`
	JwtIssuer       string `yaml:"jwt_issuer"`
	JwtSecret       string `yaml:"jwt_secret"`
	XApiKey         string `yaml:"x_api_key"`
	AdminKey        string `yaml:"admin_key"`
	OneSignalAPIKey string `yaml:"onesignal_api_key"`
	OneSignalAPPID  string `yaml:"onesignal_app_id"`
	ForceUpdateKey  string `yaml:"force_update_key"`
	OnwaUrl         string `yaml:"onwa_url"`
	OnwaId          string `yaml:"onwa_id"`
}

type Redis struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	Pass string `yaml:"pass"`
}

type Database struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
	Name string `yaml:"name"`
}

type Cloudinary struct {
	Name      string `mapstructure:"name"`
	APIKey    string `mapstructure:"api_key"`
	APISecret string `mapstructure:"api_secret"`
	APIFolder string `mapstructure:"api_folder"`
}

type Nats struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
}

type Allows struct {
	Methods []string `yaml:"methods"`
	Origins []string `yaml:"origins"`
	Headers []string `yaml:"headers"`
}

type Whatsapp struct {
	ApiKey string `yaml:"api_key"`
}

type BrightData struct {
	ApiKey string `yaml:"api_key"`
}

var ReadedConfig *Config

func InitConfig() *Config {
	var configs Config
	fileName := "/config.yaml" // Artık sabit
	yamlFile, err := os.ReadFile(fileName)
	if err != nil {
		panic("config.yaml okunamadı: " + err.Error())
	}
	yaml.Unmarshal(yamlFile, &configs)
	ReadedConfig = &configs
	return &configs
}
