package entities

import (
	"time"

	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"
)

type Presence struct {
	Base
	SessionId      string    `json:"session_id"`
	Status         string    `json:"status"`
	LastSeen       time.Time `json:"last_seen"`
	SubscribePhone string    `json:"subscribe_phone"`
	IsChecked      bool      `json:"is_checked" gorm:"default:false"`
}

func (p *Presence) ConvertDto(timezone string) dtos.PresenceHistoryItem {
	var item dtos.PresenceHistoryItem
	item.ID = p.ID
	item.Phone = p.SubscribePhone
	item.Status = p.Status
	item.Timestamp = utils.ConvertTimeToTimezone(p.CreatedAt, timezone)
	if !p.LastSeen.IsZero() {
		convertedLastSeen := utils.ConvertTimeToTimezone(p.LastSeen, timezone)
		item.LastSeen = &convertedLastSeen
	}
	return item
}
