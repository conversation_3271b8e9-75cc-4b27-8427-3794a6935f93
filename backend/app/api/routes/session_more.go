package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	session "github.com/sayeworldevelopment/wp-core/pkg/domains/session"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"
)

// PauseSession pauses a session
func PauseSession(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Pause Session Failed",
				Message: "Pause Session Failed, invalid UUID:" + err.<PERSON>rror(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.<PERSON>(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.PauseSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Pause Session Failed",
				Message: "Pause Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Pause Session Success",
			Message: "Pause Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// ResumeSession resumes a paused session
func ResumeSession(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Resume Session Failed",
				Message: "Resume Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.ResumeSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Resume Session Failed",
				Message: "Resume Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Resume Session Success",
			Message: "Resume Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// GetSessionEvents retrieves events for a session
func GetSessionEvents(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Events Failed",
				Message: "Get Session Events Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		page := 1
		perPage := 20

		events, err := s.GetSessionEvents(c, id, page, perPage)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Events Failed",
				Message: "Get Session Events Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"events":   events,
			"page":     page,
			"per_page": perPage,
		})
	}
}

// SubscribeSessionPresence subscribes to presence updates for a phone number
func SubscribeSessionPresence(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Subscribe Session Presence Failed",
				Message: "Subscribe Session Presence Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		var req dtos.SessionSubscriptionReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Subscribe Session Presence Failed",
				Message: "Subscribe Session Presence Failed, bind json err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Subscribe Session Presence Request",
			Message: "Subscribe Session Presence Request" + utils.ToJSONString(req),
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		req.SessionID = id
		err = s.SubscribePresence(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Subscribe Session Presence Failed",
				Message: "Subscribe Session Presence Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Subscribe Session Presence Success",
			Message: "Subscribe Session Presence Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, gin.H{
			"message": "Subscribed to presence updates successfully",
			"status":  http.StatusOK,
		})
	}
}

// GetSessionSubscriptions retrieves subscriptions for a session
func GetSessionSubscriptions(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Subscriptions Failed",
				Message: "Get Session Subscriptions Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		subscriptions, err := s.GetSubscriptions(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Subscriptions Failed",
				Message: "Get Session Subscriptions Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"subscriptions": subscriptions,
		})
	}
}

// GetSessionPresences retrieves presence data for a session's subscriptions
func GetSessionPresences(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Presences Failed",
				Message: "Get Session Presences Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		// Get pagination parameters
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}

		presences, err := s.GetPresences(c, id, page, perPage)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Presences Failed",
				Message: "Get Session Presences Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, presences)
	}
}

// GetSessionCurrentPresences retrieves the current presence status for all subscribed phones
// @Summary Get Current Presence Status
// @Description Get the latest presence status for all phones subscribed to this session
// @Tags Session Presence
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Session ID"
// @Success 200 {object} dtos.CurrentPresenceResponse
// @Failure 400 {object} map[string]any
// @Router /session/{id}/presence/current [get]
func GetSessionCurrentPresences(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")

		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Current Presences Failed",
				Message: "Get Session Current Presences Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		phone := c.Query("phone")

		presences, err := s.GetCurrentPresences(c, id, phone)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Current Presences Failed",
				Message: "Get Session Current Presences Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, presences)
	}
}

// GetSessionLastPresenceByPhone retrieves the last presence status for a specific phone
// @Summary Get Last Presence Status
// @Description Get the last presence status for a specific phone subscribed to this session
// @Tags Session Presence
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Session ID"
// @Param phone query string true "Phone number to get last presence for"
// @Success 200 {object} dtos.CurrentPresenceItem
// @Failure 400 {object} map[string]any
// @Router /session/{id}/presence/last [get]
func GetSessionLastPresenceByPhone(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Last Presence Failed",
				Message: "Get Session Last Presence Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		phone := c.Query("phone")
		if phone == "" {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Last Presence Failed",
				Message: "Get Session Last Presence Failed, phone parameter is required",
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Phone parameter is required",
				"status": http.StatusBadRequest,
			})
			return
		}

		presence, err := s.GetLastPresenceByPhone(c, id, phone)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Last Presence Failed",
				Message: "Get Session Last Presence Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, presence)
	}
}

// GetSessionPresenceHistory retrieves historical presence data with time range filtering and pagination
// @Summary Get Presence History
// @Description Get historical presence data for subscribed phones with time range filtering and pagination
// @Tags Session Presence
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Session ID"
// @Param time_range query string false "Time range (3h, 24h, 2d, 3d)" default(24h)
// @Param phone query string false "Filter by specific phone number"
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(10)
// @Success 200 {object} dtos.PresenceHistoryResponse
// @Failure 400 {object} map[string]any
// @Router /session/{id}/presence/history [get]
func GetSessionPresenceHistory(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Presence History Failed",
				Message: "Get Session Presence History Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		// Parse query parameters
		timeRange := c.Query("time_range")

		phone := c.Query("phone") // optional phone filter

		// Get pagination parameters
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}

		endDate := c.Query("end_date")
		startDate := c.Query("start_date")

		req := dtos.PresenceHistoryReq{
			TimeRange: timeRange,
			Phone:     phone,
			Page:      page,
			PerPage:   perPage,
			StartDate: startDate,
			EndDate:   endDate,
		}

		// Validate time range
		if req.TimeRange != "" {
			validRanges := map[string]bool{"3h": true, "24h": true, "2d": true, "3d": true}
			if !validRanges[req.TimeRange] {
				log.CreateLog(&entities.Log{
					Title:   "Get Session Presence History Failed",
					Message: "Get Session Presence History Failed, invalid time range: " + req.TimeRange,
					Entity:  "session",
					Type:    "error",
					Ip:      c.ClientIP(),
				})
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
					"error":  "Invalid time range. Valid values: 3h, 24h, 2d, 3d",
					"status": http.StatusBadRequest,
				})
				return
			}
		}

		presences, err := s.GetPresenceHistory(c, id, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Presence History Failed",
				Message: "Get Session Presence History Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Get Session Presence History Success",
			Message: "Get Session Presence History Success:" + utils.ToJSONString(presences),
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, presences)
	}
}

// RemoveSessionPresenceSubscription removes a presence subscription for a session
// @Summary Remove Presence Subscription
// @Description Remove presence subscription for a specific phone number in a session
// @Tags Session Presence
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Session ID"
// @Param phone query string true "Phone number to unsubscribe from"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Router /session/{id}/presence/:phone [delete]
func RemoveSessionPresenceSubscription(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Remove Session Presence Subscription Failed",
				Message: "Remove Session Presence Subscription Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		phone := c.Param("phone")
		if phone == "" {
			log.CreateLog(&entities.Log{
				Title:   "Remove Session Presence Subscription Failed",
				Message: "Remove Session Presence Subscription Failed, phone parameter is required",
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Phone parameter is required",
				"status": http.StatusBadRequest,
			})
			return
		}

		err = s.RemovePresenceSubscription(c, id, phone)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Remove Session Presence Subscription Failed",
				Message: "Remove Session Presence Subscription Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Remove Session Presence Subscription Success",
			Message: "Remove Session Presence Subscription Success for phone: " + phone,
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, gin.H{
			"message": "Presence subscription removed successfully",
			"phone":   phone,
			"status":  http.StatusOK,
		})
	}
}
