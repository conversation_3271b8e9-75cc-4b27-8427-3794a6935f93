app:
  name: wp-core
  port: 8023
  host:
  jwt_issuer: "saye"
  jwt_secret: "secret"
  client_id: "saye"
  x_api_key: "saye"
  admin_key: "saye"
  onwa_url: "http://localhost:8000"
  onwa_id: "FbGjFAp84r1t"

database:
  host: wp-core-db
  port: 5432
  user: wp-core
  pass: wp-core
  name: wp-core

brightdata:
  api_key: "c2be8fe0-7777-4a58-b8b3-ac505a5fa090"

allows:
  methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS
  headers:
  - Content-Type
  - Authorization
  - X-CSRF-Token
  - data-api-key
  origins:
    - http://localhost:8000
    - http://localhost:9000
    - http://localhost:4040
    - http://localhost:3000